import api from "../api"

export interface MouApplication {
  id: string
  mouApplicationId: string
  mouId: string
  responsibility?: string
  userId?: string
  user?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  mou?: {
    id: string
    mouId: string
    party: {
      id: string
      partyName: string
    }
  }
  projects?: Project[]
  approvalSteps?: ApprovalStep[]
  createdAt: string
  updatedAt: string
  deleted: boolean
}

export interface Project {
  id: string
  projectId: string
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
  status?: string
  mouApplicationId: string
  createdAt: string
  updatedAt: string
}

export interface ApprovalStep {
  id: string
  stepNumber: number
  reviewerRole: string
  reviewerId?: string
  reviewer?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  status: 'PENDING' | 'RECOMMEND_FOR_APPROVAL' | 'RECOMMEND_FOR_MODIFICATION' | 'REJECT' | 'APPROVE'
  comments?: string
  reviewedAt?: string
  mouApplicationId: string
  createdAt: string
  updatedAt: string
}

export interface CreateMouApplicationRequest {
  mouId: string
  responsibility?: string
  projects?: CreateProjectRequest[]
}

export interface CreateProjectRequest {
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
}

export interface UpdateMouApplicationRequest {
  responsibility?: string
  projects?: UpdateProjectRequest[]
}

export interface UpdateProjectRequest {
  id?: string
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
}

export const mouApplicationService = {
  // Get all MoU applications for the current user (partner)
  async getMyApplications(): Promise<MouApplication[]> {
    const response = await api.get("/mou-applications/my-applications")
    return response.data
  },

  // Get all MoU applications (admin/reviewer access)
  async getAllApplications(): Promise<MouApplication[]> {
    const response = await api.get("/mou-applications")
    return response.data
  },

  // Get a specific MoU application by ID
  async getApplication(id: string): Promise<MouApplication> {
    const response = await api.get(`/mou-applications/${id}`)
    return response.data
  },

  // Create a new MoU application
  async createApplication(data: CreateMouApplicationRequest): Promise<MouApplication> {
    const response = await api.post("/mou-applications", data)
    return response.data
  },

  // Update an existing MoU application
  async updateApplication(id: string, data: UpdateMouApplicationRequest): Promise<MouApplication> {
    const response = await api.put(`/mou-applications/${id}`, data)
    return response.data
  },

  // Delete a MoU application
  async deleteApplication(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/mou-applications/${id}`)
    return response.data
  },

  // Submit application for review
  async submitApplication(id: string): Promise<{ message: string }> {
    const response = await api.post(`/mou-applications/${id}/submit`)
    return response.data
  },

  // Get available MoUs for application
  async getAvailableMous(): Promise<any[]> {
    const response = await api.get("/mous/available")
    return response.data
  },

  // Get application statistics for dashboard
  async getApplicationStats(): Promise<{
    total: number
    pending: number
    approved: number
    rejected: number
    draft: number
  }> {
    const response = await api.get("/mou-applications/stats")
    return response.data
  }
}
