"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  FileText, 
  Plus, 
  Search,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  XCircle,
  FileEdit,
  Send
} from "lucide-react"
import Link from "next/link"
import { mouApplicationService, MouApplication } from "@/lib/services/mou-application.service"
import { Loader2 } from "lucide-react"
import { format } from "date-fns"

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'draft':
      return <FileEdit className="h-4 w-4" />
    case 'pending':
      return <Clock className="h-4 w-4" />
    case 'approved':
      return <CheckCircle className="h-4 w-4" />
    case 'rejected':
      return <XCircle className="h-4 w-4" />
    default:
      return <FileText className="h-4 w-4" />
  }
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200'
    case 'approved':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export default function PartnerApplicationsPage() {
  const { user } = useAuth()
  const [applications, setApplications] = useState<MouApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    loadApplications()
  }, [])

  const loadApplications = async () => {
    try {
      setLoading(true)
      const data = await mouApplicationService.getMyApplications()
      setApplications(data)
    } catch (err: any) {
      console.error("Failed to load applications:", err)
      setError("Failed to load applications")
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (application: MouApplication) => {
    if (confirm(`Are you sure you want to delete application ${application.mouApplicationId}?`)) {
      try {
        await mouApplicationService.deleteApplication(application.id)
        setSuccess("Application deleted successfully")
        await loadApplications()
      } catch (err: any) {
        setError(err.response?.data?.message || "Failed to delete application")
      }
    }
  }

  const handleSubmit = async (application: MouApplication) => {
    if (confirm(`Are you sure you want to submit application ${application.mouApplicationId} for review?`)) {
      try {
        await mouApplicationService.submitApplication(application.id)
        setSuccess("Application submitted for review successfully")
        await loadApplications()
      } catch (err: any) {
        setError(err.response?.data?.message || "Failed to submit application")
      }
    }
  }

  const filteredApplications = applications.filter(app =>
    app.mouApplicationId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.responsibility?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.mou?.party?.partyName?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getApplicationStatus = (app: MouApplication) => {
    if (!app.approvalSteps || app.approvalSteps.length === 0) {
      return 'draft'
    }
    
    const latestStep = app.approvalSteps.sort((a, b) => b.stepNumber - a.stepNumber)[0]
    return latestStep.status.toLowerCase()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-cyan-900">MoU Applications</h2>
          <p className="text-muted-foreground">Manage your organization's MoU applications</p>
        </div>
        <Button asChild className="bg-cyan-600 hover:bg-cyan-700">
          <Link href="/dashboard/partner/applications/new">
            <Plus className="mr-2 h-4 w-4" />
            New Application
          </Link>
        </Button>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Your Applications</CardTitle>
              <CardDescription>
                View and manage all your MoU applications
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search applications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-cyan-600" />
            </div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No applications found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? "No applications match your search criteria." : "You haven't created any MoU applications yet."}
              </p>
              <Button asChild className="bg-cyan-600 hover:bg-cyan-700">
                <Link href="/dashboard/partner/applications/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Application
                </Link>
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Application ID</TableHead>
                    <TableHead>MoU Party</TableHead>
                    <TableHead>Responsibility</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="w-[120px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => {
                    const status = getApplicationStatus(application)
                    return (
                      <TableRow key={application.id}>
                        <TableCell className="font-medium">
                          {application.mouApplicationId}
                        </TableCell>
                        <TableCell>
                          {application.mou?.party?.partyName || 'N/A'}
                        </TableCell>
                        <TableCell>
                          <div className="max-w-[200px] truncate">
                            {application.responsibility || 'Not specified'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(status)}
                              <span className="capitalize">{status}</span>
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {format(new Date(application.createdAt), 'MMM dd, yyyy')}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button asChild size="sm" variant="ghost">
                              <Link href={`/dashboard/partner/applications/${application.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            {status === 'draft' && (
                              <>
                                <Button asChild size="sm" variant="ghost">
                                  <Link href={`/dashboard/partner/applications/${application.id}/edit`}>
                                    <Edit className="h-4 w-4" />
                                  </Link>
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleSubmit(application)}
                                >
                                  <Send className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDelete(application)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
