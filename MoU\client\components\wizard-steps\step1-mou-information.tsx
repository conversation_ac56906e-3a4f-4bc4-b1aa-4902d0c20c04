"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Loader2, Building, FileText, Calendar, Info } from "lucide-react"
import { Step1Data, MouApplication, mouApplicationService } from "@/lib/services/mou-application.service"

interface Step1MouInformationProps {
  data: Step1Data
  onChange: (data: Step1Data) => void
  application: MouApplication | null
}

interface AvailableMou {
  id: string
  mouId: string
  party: {
    id: string
    partyName: string
    partyType?: string
  }
  description?: string
  validFrom?: string
  validTo?: string
  status?: string
}

export function Step1MouInformation({ data, onChange, application }: Step1MouInformationProps) {
  const [availableMous, setAvailableMous] = useState<AvailableMou[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedMou, setSelectedMou] = useState<AvailableMou | null>(null)

  useEffect(() => {
    loadAvailableMous()
  }, [])

  useEffect(() => {
    if (data.mouId && availableMous.length > 0) {
      const mou = availableMous.find(m => m.id === data.mouId)
      setSelectedMou(mou || null)
    }
  }, [data.mouId, availableMous])

  const loadAvailableMous = async () => {
    try {
      setLoading(true)
      const mous = await mouApplicationService.getAvailableMous()
      setAvailableMous(mous)
    } catch (err: any) {
      setError("Failed to load available MoUs")
    } finally {
      setLoading(false)
    }
  }

  const handleMouChange = (mouId: string) => {
    const mou = availableMous.find(m => m.id === mouId)
    setSelectedMou(mou || null)
    onChange({
      ...data,
      mouId
    })
  }

  const handleDurationChange = (value: string) => {
    const years = parseInt(value)
    if (years >= 1 && years <= 10) {
      onChange({
        ...data,
        mouDurationYears: years
      })
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">MoU Information</h3>
        <p className="text-muted-foreground">
          Select the Memorandum of Understanding you want to apply for and set the duration.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Organization Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Building className="h-4 w-4 text-cyan-600" />
              Organization Information
            </CardTitle>
            <CardDescription>
              Your organization details (read-only)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="organizationName">Organization Name</Label>
              <Input
                id="organizationName"
                value={data.organizationName}
                readOnly
                className="bg-gray-50"
              />
              <p className="text-xs text-muted-foreground">
                This information is automatically populated from your account
              </p>
            </div>
          </CardContent>
        </Card>

        {/* MoU Duration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Calendar className="h-4 w-4 text-cyan-600" />
              MoU Duration
            </CardTitle>
            <CardDescription>
              Set the duration for this MoU agreement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mouDurationYears">Duration (Years) *</Label>
              <Select
                value={data.mouDurationYears.toString()}
                onValueChange={handleDurationChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => i + 1).map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year} {year === 1 ? 'Year' : 'Years'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Duration must be between 1 and 10 years
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* MoU Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-cyan-600" />
            Available MoUs
          </CardTitle>
          <CardDescription>
            Select the Memorandum of Understanding you want to apply for
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-cyan-600" />
              <span className="ml-2 text-muted-foreground">Loading available MoUs...</span>
            </div>
          ) : availableMous.length === 0 ? (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                No MoUs are currently available for application. Please contact the Ministry of Health for more information.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="mouId">Select MoU *</Label>
              <Select value={data.mouId} onValueChange={handleMouChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a MoU to apply for" />
                </SelectTrigger>
                <SelectContent>
                  {availableMous.map((mou) => (
                    <SelectItem key={mou.id} value={mou.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{mou.mouId}</span>
                        <span className="text-sm text-muted-foreground">
                          {mou.party.partyName}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected MoU Details */}
      {selectedMou && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5 text-cyan-600" />
              Selected MoU Details
            </CardTitle>
            <CardDescription>
              Review the details of your selected MoU
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">MoU ID</Label>
                <p className="font-medium">{selectedMou.mouId}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Party Name</Label>
                <p className="font-medium">{selectedMou.party.partyName}</p>
              </div>
              
              {selectedMou.party.partyType && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Party Type</Label>
                  <Badge variant="outline">{selectedMou.party.partyType}</Badge>
                </div>
              )}
              
              {selectedMou.status && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                  <Badge 
                    variant={selectedMou.status === 'ACTIVE' ? 'default' : 'secondary'}
                    className={selectedMou.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : ''}
                  >
                    {selectedMou.status}
                  </Badge>
                </div>
              )}
            </div>
            
            {selectedMou.description && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                <p className="text-sm leading-relaxed mt-1">{selectedMou.description}</p>
              </div>
            )}
            
            {(selectedMou.validFrom || selectedMou.validTo) && (
              <div className="grid gap-4 md:grid-cols-2">
                {selectedMou.validFrom && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Valid From</Label>
                    <p className="text-sm">{new Date(selectedMou.validFrom).toLocaleDateString()}</p>
                  </div>
                )}
                
                {selectedMou.validTo && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Valid To</Label>
                    <p className="text-sm">{new Date(selectedMou.validTo).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 1 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${data.mouId ? 'text-green-700' : ''}`}>
                  {data.mouId ? '✓' : '○'} Select a MoU
                </li>
                <li className={`flex items-center gap-2 ${data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? 'text-green-700' : ''}`}>
                  {data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? '✓' : '○'} Set MoU duration (1-10 years)
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
