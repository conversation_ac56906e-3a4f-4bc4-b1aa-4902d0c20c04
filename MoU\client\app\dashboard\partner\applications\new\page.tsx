"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  FileText, 
  Plus, 
  Save,
  ArrowLeft,
  Trash2,
  Calendar,
  DollarSign
} from "lucide-react"
import Link from "next/link"
import { mouApplicationService, CreateMouApplicationRequest, CreateProjectRequest } from "@/lib/services/mou-application.service"
import { Loader2 } from "lucide-react"

interface ProjectForm {
  projectName: string
  projectDescription: string
  startDate: string
  endDate: string
  totalBudget: string
  currency: string
}

export default function NewMouApplicationPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [availableMous, setAvailableMous] = useState<any[]>([])
  const [loadingMous, setLoadingMous] = useState(true)

  // Form state
  const [mouId, setMouId] = useState("")
  const [responsibility, setResponsibility] = useState("")
  const [projects, setProjects] = useState<ProjectForm[]>([
    {
      projectName: "",
      projectDescription: "",
      startDate: "",
      endDate: "",
      totalBudget: "",
      currency: "USD"
    }
  ])

  useEffect(() => {
    loadAvailableMous()
  }, [])

  const loadAvailableMous = async () => {
    try {
      setLoadingMous(true)
      const data = await mouApplicationService.getAvailableMous()
      setAvailableMous(data)
    } catch (err: any) {
      console.error("Failed to load available MoUs:", err)
      setError("Failed to load available MoUs")
    } finally {
      setLoadingMous(false)
    }
  }

  const addProject = () => {
    setProjects([...projects, {
      projectName: "",
      projectDescription: "",
      startDate: "",
      endDate: "",
      totalBudget: "",
      currency: "USD"
    }])
  }

  const removeProject = (index: number) => {
    if (projects.length > 1) {
      setProjects(projects.filter((_, i) => i !== index))
    }
  }

  const updateProject = (index: number, field: keyof ProjectForm, value: string) => {
    const updatedProjects = [...projects]
    updatedProjects[index] = { ...updatedProjects[index], [field]: value }
    setProjects(updatedProjects)
  }

  const validateForm = () => {
    if (!mouId) {
      setError("Please select a MoU")
      return false
    }

    if (!responsibility.trim()) {
      setError("Please provide responsibility description")
      return false
    }

    for (let i = 0; i < projects.length; i++) {
      const project = projects[i]
      if (!project.projectName.trim()) {
        setError(`Please provide project name for project ${i + 1}`)
        return false
      }
      if (project.totalBudget && isNaN(Number(project.totalBudget))) {
        setError(`Please provide a valid budget amount for project ${i + 1}`)
        return false
      }
      if (project.startDate && project.endDate && new Date(project.startDate) > new Date(project.endDate)) {
        setError(`End date must be after start date for project ${i + 1}`)
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)

      const projectsData: CreateProjectRequest[] = projects
        .filter(p => p.projectName.trim())
        .map(p => ({
          projectName: p.projectName.trim(),
          projectDescription: p.projectDescription.trim() || undefined,
          startDate: p.startDate || undefined,
          endDate: p.endDate || undefined,
          totalBudget: p.totalBudget ? Number(p.totalBudget) : undefined,
          currency: p.currency || undefined
        }))

      const applicationData: CreateMouApplicationRequest = {
        mouId,
        responsibility: responsibility.trim(),
        projects: projectsData.length > 0 ? projectsData : undefined
      }

      const newApplication = await mouApplicationService.createApplication(applicationData)
      router.push(`/dashboard/partner/applications/${newApplication.id}`)
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to create application")
    } finally {
      setLoading(false)
    }
  }

  const handleSaveDraft = async () => {
    setError("")

    if (!mouId) {
      setError("Please select a MoU before saving draft")
      return
    }

    try {
      setLoading(true)

      const projectsData: CreateProjectRequest[] = projects
        .filter(p => p.projectName.trim())
        .map(p => ({
          projectName: p.projectName.trim(),
          projectDescription: p.projectDescription.trim() || undefined,
          startDate: p.startDate || undefined,
          endDate: p.endDate || undefined,
          totalBudget: p.totalBudget ? Number(p.totalBudget) : undefined,
          currency: p.currency || undefined
        }))

      const applicationData: CreateMouApplicationRequest = {
        mouId,
        responsibility: responsibility.trim() || undefined,
        projects: projectsData.length > 0 ? projectsData : undefined
      }

      const newApplication = await mouApplicationService.createApplication(applicationData)
      router.push(`/dashboard/partner/applications/${newApplication.id}`)
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to save draft")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/dashboard/partner/applications">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Applications
              </Link>
            </Button>
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-cyan-900">New MoU Application</h2>
          <p className="text-muted-foreground">Create a new MoU application for your organization</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* MoU Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-cyan-600" />
              MoU Selection
            </CardTitle>
            <CardDescription>
              Select the MoU you want to apply for
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mouId">Available MoUs *</Label>
              {loadingMous ? (
                <div className="flex items-center gap-2 p-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading available MoUs...</span>
                </div>
              ) : (
                <Select value={mouId} onValueChange={setMouId} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a MoU to apply for" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableMous.map((mou) => (
                      <SelectItem key={mou.id} value={mou.id}>
                        {mou.mouId} - {mou.party?.partyName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsibility">Organization Responsibility *</Label>
              <Textarea
                id="responsibility"
                placeholder="Describe your organization's responsibilities and commitments under this MoU..."
                value={responsibility}
                onChange={(e) => setResponsibility(e.target.value)}
                rows={4}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Projects Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5 text-cyan-600" />
                  Projects (Optional)
                </CardTitle>
                <CardDescription>
                  Add projects that will be implemented under this MoU
                </CardDescription>
              </div>
              <Button type="button" onClick={addProject} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Project
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {projects.map((project, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Project {index + 1}</h4>
                  {projects.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removeProject(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor={`projectName-${index}`}>Project Name *</Label>
                    <Input
                      id={`projectName-${index}`}
                      placeholder="Enter project name"
                      value={project.projectName}
                      onChange={(e) => updateProject(index, 'projectName', e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`currency-${index}`}>Currency</Label>
                    <Select
                      value={project.currency}
                      onValueChange={(value) => updateProject(index, 'currency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="RWF">RWF</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`projectDescription-${index}`}>Project Description</Label>
                  <Textarea
                    id={`projectDescription-${index}`}
                    placeholder="Describe the project objectives, activities, and expected outcomes..."
                    value={project.projectDescription}
                    onChange={(e) => updateProject(index, 'projectDescription', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor={`startDate-${index}`}>Start Date</Label>
                    <Input
                      id={`startDate-${index}`}
                      type="date"
                      value={project.startDate}
                      onChange={(e) => updateProject(index, 'startDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`endDate-${index}`}>End Date</Label>
                    <Input
                      id={`endDate-${index}`}
                      type="date"
                      value={project.endDate}
                      onChange={(e) => updateProject(index, 'endDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`totalBudget-${index}`}>Total Budget</Label>
                    <Input
                      id={`totalBudget-${index}`}
                      type="number"
                      placeholder="0.00"
                      value={project.totalBudget}
                      onChange={(e) => updateProject(index, 'totalBudget', e.target.value)}
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button asChild variant="outline">
            <Link href="/dashboard/partner/applications">
              Cancel
            </Link>
          </Button>
          
          <div className="flex items-center gap-2">
            <Button
              type="button"
              onClick={handleSaveDraft}
              variant="outline"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Draft
            </Button>
            
            <Button
              type="submit"
              disabled={loading}
              className="bg-cyan-600 hover:bg-cyan-700"
            >
              {loading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <FileText className="mr-2 h-4 w-4" />
              )}
              Create Application
            </Button>
          </div>
        </div>
      </form>
    </div>
  )
}
