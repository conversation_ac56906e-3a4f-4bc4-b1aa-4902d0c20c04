"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  FileText, 
  Plus, 
  Save,
  ArrowLeft,
  Trash2,
  Calendar,
  DollarSign
} from "lucide-react"
import Link from "next/link"
import { mouApplicationService, MouApplication, UpdateMouApplicationRequest, UpdateProjectRequest } from "@/lib/services/mou-application.service"
import { Loader2 } from "lucide-react"

interface ProjectForm {
  id?: string
  projectName: string
  projectDescription: string
  startDate: string
  endDate: string
  totalBudget: string
  currency: string
}

export default function EditMouApplicationPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const applicationId = params.id as string

  const [application, setApplication] = useState<MouApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState("")

  // Form state
  const [responsibility, setResponsibility] = useState("")
  const [projects, setProjects] = useState<ProjectForm[]>([])

  useEffect(() => {
    if (applicationId) {
      loadApplication()
    }
  }, [applicationId])

  const loadApplication = async () => {
    try {
      setLoading(true)
      const data = await mouApplicationService.getApplication(applicationId)
      setApplication(data)
      
      // Populate form
      setResponsibility(data.responsibility || "")
      setProjects(data.projects?.map(p => ({
        id: p.id,
        projectName: p.projectName,
        projectDescription: p.projectDescription || "",
        startDate: p.startDate ? p.startDate.split('T')[0] : "",
        endDate: p.endDate ? p.endDate.split('T')[0] : "",
        totalBudget: p.totalBudget?.toString() || "",
        currency: p.currency || "USD"
      })) || [])
      
      // Add empty project if none exist
      if (!data.projects || data.projects.length === 0) {
        setProjects([{
          projectName: "",
          projectDescription: "",
          startDate: "",
          endDate: "",
          totalBudget: "",
          currency: "USD"
        }])
      }
    } catch (err: any) {
      console.error("Failed to load application:", err)
      setError("Failed to load application")
    } finally {
      setLoading(false)
    }
  }

  const addProject = () => {
    setProjects([...projects, {
      projectName: "",
      projectDescription: "",
      startDate: "",
      endDate: "",
      totalBudget: "",
      currency: "USD"
    }])
  }

  const removeProject = (index: number) => {
    if (projects.length > 1) {
      setProjects(projects.filter((_, i) => i !== index))
    }
  }

  const updateProject = (index: number, field: keyof ProjectForm, value: string) => {
    const updatedProjects = [...projects]
    updatedProjects[index] = { ...updatedProjects[index], [field]: value }
    setProjects(updatedProjects)
  }

  const validateForm = () => {
    if (!responsibility.trim()) {
      setError("Please provide responsibility description")
      return false
    }

    for (let i = 0; i < projects.length; i++) {
      const project = projects[i]
      if (project.projectName.trim() && project.totalBudget && isNaN(Number(project.totalBudget))) {
        setError(`Please provide a valid budget amount for project ${i + 1}`)
        return false
      }
      if (project.startDate && project.endDate && new Date(project.startDate) > new Date(project.endDate)) {
        setError(`End date must be after start date for project ${i + 1}`)
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!validateForm()) {
      return
    }

    try {
      setSaving(true)

      const projectsData: UpdateProjectRequest[] = projects
        .filter(p => p.projectName.trim())
        .map(p => ({
          id: p.id,
          projectName: p.projectName.trim(),
          projectDescription: p.projectDescription.trim() || undefined,
          startDate: p.startDate || undefined,
          endDate: p.endDate || undefined,
          totalBudget: p.totalBudget ? Number(p.totalBudget) : undefined,
          currency: p.currency || undefined
        }))

      const updateData: UpdateMouApplicationRequest = {
        responsibility: responsibility.trim(),
        projects: projectsData
      }

      await mouApplicationService.updateApplication(applicationId, updateData)
      router.push(`/dashboard/partner/applications/${applicationId}`)
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to update application")
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-cyan-600" />
      </div>
    )
  }

  if (error && !application) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/dashboard/partner/applications">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Applications
          </Link>
        </Button>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>Application not found</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/dashboard/partner/applications">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Applications
          </Link>
        </Button>
      </div>
    )
  }

  // Check if application can be edited (only drafts)
  const isDraft = !application.approvalSteps || application.approvalSteps.length === 0
  if (!isDraft) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>This application cannot be edited as it has already been submitted for review.</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href={`/dashboard/partner/applications/${applicationId}`}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Application
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href={`/dashboard/partner/applications/${applicationId}`}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Application
              </Link>
            </Button>
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-cyan-900">
            Edit Application {application.mouApplicationId}
          </h2>
          <p className="text-muted-foreground">Update your MoU application details</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Application Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-cyan-600" />
              Application Details
            </CardTitle>
            <CardDescription>
              MoU: {application.mou?.party?.partyName}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="responsibility">Organization Responsibility *</Label>
              <Textarea
                id="responsibility"
                placeholder="Describe your organization's responsibilities and commitments under this MoU..."
                value={responsibility}
                onChange={(e) => setResponsibility(e.target.value)}
                rows={4}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Projects Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5 text-cyan-600" />
                  Projects
                </CardTitle>
                <CardDescription>
                  Update projects that will be implemented under this MoU
                </CardDescription>
              </div>
              <Button type="button" onClick={addProject} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Project
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {projects.map((project, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Project {index + 1}</h4>
                  {projects.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removeProject(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor={`projectName-${index}`}>Project Name</Label>
                    <Input
                      id={`projectName-${index}`}
                      placeholder="Enter project name"
                      value={project.projectName}
                      onChange={(e) => updateProject(index, 'projectName', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`currency-${index}`}>Currency</Label>
                    <Select
                      value={project.currency}
                      onValueChange={(value) => updateProject(index, 'currency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="RWF">RWF</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`projectDescription-${index}`}>Project Description</Label>
                  <Textarea
                    id={`projectDescription-${index}`}
                    placeholder="Describe the project objectives, activities, and expected outcomes..."
                    value={project.projectDescription}
                    onChange={(e) => updateProject(index, 'projectDescription', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor={`startDate-${index}`}>Start Date</Label>
                    <Input
                      id={`startDate-${index}`}
                      type="date"
                      value={project.startDate}
                      onChange={(e) => updateProject(index, 'startDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`endDate-${index}`}>End Date</Label>
                    <Input
                      id={`endDate-${index}`}
                      type="date"
                      value={project.endDate}
                      onChange={(e) => updateProject(index, 'endDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`totalBudget-${index}`}>Total Budget</Label>
                    <Input
                      id={`totalBudget-${index}`}
                      type="number"
                      placeholder="0.00"
                      value={project.totalBudget}
                      onChange={(e) => updateProject(index, 'totalBudget', e.target.value)}
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button asChild variant="outline">
            <Link href={`/dashboard/partner/applications/${applicationId}`}>
              Cancel
            </Link>
          </Button>
          
          <Button
            type="submit"
            disabled={saving}
            className="bg-cyan-600 hover:bg-cyan-700"
          >
            {saving ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Save className="mr-2 h-4 w-4" />
            )}
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  )
}
